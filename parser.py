# -*- coding: utf-8 -*-
"""
语法分析器模块 - 实现语法制导翻译
学号：2022112519
课程设计：语法制导翻译—算术表达式逆波兰式的翻译
"""

import re

class Token:
    """词法单元类"""
    def __init__(self, type_, value, position):
        self.type = type_      # 词法单元类型
        self.value = value     # 词法单元值
        self.position = position  # 在输入串中的位置

class LexicalAnalyzer:
    """词法分析器"""
    
    def __init__(self):
        # 定义词法单元的正则表达式
        self.token_patterns = [
            ('NUMBER', r'\d+(\.\d*)?'),  # 数字常量
            ('IDENTIFIER', r'[a-zA-Z_][a-zA-Z0-9_]*'),  # 变量标识符
            ('PLUS', r'\+'),
            ('MINUS', r'-'),
            ('MULTIPLY', r'\*'),
            ('DIVIDE', r'/'),
            ('NEGATE', r'@'),  # 取负运算符
            ('LPAREN', r'\('),
            ('RPAREN', r'\)'),
            ('WHITESPACE', r'\s+'),  # 空白字符
        ]
        
        # 编译正则表达式
        self.token_regex = '|'.join(f'(?P<{name}>{pattern})' 
                                   for name, pattern in self.token_patterns)
        self.pattern = re.compile(self.token_regex)
    
    def tokenize(self, text):
        """词法分析，将输入字符串转换为词法单元序列"""
        tokens = []
        position = 0
        
        for match in self.pattern.finditer(text):
            token_type = match.lastgroup
            token_value = match.group()
            
            if token_type == 'WHITESPACE':
                position = match.end()
                continue
                
            tokens.append(Token(token_type, token_value, position))
            position = match.end()
        
        # 检查是否有无法识别的字符
        if position < len(text):
            raise SyntaxError(f"无法识别的字符 '{text[position]}' 在位置 {position}")
        
        return tokens

class SyntaxDirectedTranslator:
    """语法制导翻译器 - 递归下降分析器"""
    
    def __init__(self):
        self.tokens = []
        self.current_token_index = 0
        self.current_token = None
        self.postfix_code = []  # 存储生成的逆波兰式
        
    def translate(self, expression):
        """主翻译函数"""
        try:
            # 词法分析
            lexer = LexicalAnalyzer()
            self.tokens = lexer.tokenize(expression)
            
            if not self.tokens:
                raise SyntaxError("表达式为空")
            
            # 初始化
            self.current_token_index = 0
            self.current_token = self.tokens[0]
            self.postfix_code = []
            
            # 语法分析和翻译
            self.parse_expression()
            
            # 检查是否还有未处理的词法单元
            if self.current_token_index < len(self.tokens):
                raise SyntaxError(f"表达式在位置 {self.current_token.position} 处有多余的符号")
            
            return ' '.join(self.postfix_code)
            
        except SyntaxError as e:
            return f"语法错误: {str(e)}"
        except Exception as e:
            return f"翻译错误: {str(e)}"
    
    def advance(self):
        """移动到下一个词法单元"""
        self.current_token_index += 1
        if self.current_token_index < len(self.tokens):
            self.current_token = self.tokens[self.current_token_index]
        else:
            self.current_token = None
    
    def match(self, expected_type):
        """匹配指定类型的词法单元"""
        if self.current_token and self.current_token.type == expected_type:
            token = self.current_token
            self.advance()
            return token
        else:
            expected_name = self.get_token_name(expected_type)
            actual_name = self.get_token_name(self.current_token.type) if self.current_token else "文件结束"
            raise SyntaxError(f"期望 {expected_name}，但得到 {actual_name} 在位置 {self.current_token.position if self.current_token else len(self.tokens)}")
    
    def get_token_name(self, token_type):
        """获取词法单元类型的中文名称"""
        names = {
            'NUMBER': '数字',
            'IDENTIFIER': '标识符',
            'PLUS': '+',
            'MINUS': '-',
            'MULTIPLY': '*',
            'DIVIDE': '/',
            'NEGATE': '@',
            'LPAREN': '(',
            'RPAREN': ')'
        }
        return names.get(token_type, token_type)
    
    def parse_expression(self):
        """解析表达式 E → E + T | E - T | T"""
        self.parse_term()
        
        while (self.current_token and 
               self.current_token.type in ['PLUS', 'MINUS']):
            operator = self.current_token.type
            self.advance()
            self.parse_term()
            
            # 生成逆波兰式代码
            if operator == 'PLUS':
                self.postfix_code.append('+')
            else:
                self.postfix_code.append('-')
    
    def parse_term(self):
        """解析项 T → T * F | T / F | F"""
        self.parse_factor()
        
        while (self.current_token and 
               self.current_token.type in ['MULTIPLY', 'DIVIDE']):
            operator = self.current_token.type
            self.advance()
            self.parse_factor()
            
            # 生成逆波兰式代码
            if operator == 'MULTIPLY':
                self.postfix_code.append('*')
            else:
                self.postfix_code.append('/')
    
    def parse_factor(self):
        """解析因子 F → @ F | ( E ) | i | d"""
        if not self.current_token:
            raise SyntaxError("意外的表达式结束")
        
        if self.current_token.type == 'NEGATE':
            # 处理取负运算 @ F
            self.advance()
            self.parse_factor()
            self.postfix_code.append('@')
            
        elif self.current_token.type == 'LPAREN':
            # 处理括号表达式 ( E )
            self.advance()
            self.parse_expression()
            self.match('RPAREN')
            
        elif self.current_token.type == 'NUMBER':
            # 处理数字常量
            token = self.match('NUMBER')
            self.postfix_code.append(token.value)
            
        elif self.current_token.type == 'IDENTIFIER':
            # 处理变量标识符
            token = self.match('IDENTIFIER')
            self.postfix_code.append(token.value)
            
        else:
            token_name = self.get_token_name(self.current_token.type)
            raise SyntaxError(f"意外的符号 {token_name} 在位置 {self.current_token.position}")

if __name__ == "__main__":
    # 测试语法制导翻译器
    translator = SyntaxDirectedTranslator()
    
    test_expressions = [
        "a + b * c",
        "a * (b + c)",
        "@a + b",
        "a + b * c - d / e",
        "(a + b) * (c - d)"
    ]
    
    for expr in test_expressions:
        result = translator.translate(expr)
        print(f"中缀表达式: {expr}")
        print(f"逆波兰式: {result}")
        print("-" * 40)
