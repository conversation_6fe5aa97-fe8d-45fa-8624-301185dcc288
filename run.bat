@echo off
chcp 65001 >nul
echo ========================================
echo 语法制导翻译—算术表达式逆波兰式的翻译
echo 学号：2022112519
echo ========================================
echo.
echo 请选择要运行的程序：
echo 1. 启动GUI程序
echo 2. 运行测试程序
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo 正在启动GUI程序...
    python main.py
) else if "%choice%"=="2" (
    echo 正在运行测试程序...
    python test_cases.py
    pause
) else if "%choice%"=="3" (
    echo 再见！
    exit
) else (
    echo 无效选择，请重新运行程序。
    pause
)
