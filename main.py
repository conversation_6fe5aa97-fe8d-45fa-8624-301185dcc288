# -*- coding: utf-8 -*-
"""
语法制导翻译—算术表达式逆波兰式的翻译
主程序 - GUI界面
学号：2022112519
课程设计：编译原理课程设计
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from grammar import AttributeGrammar
from parser import SyntaxDirectedTranslator
from evaluator import PostfixEvaluator

class ArithmeticTranslatorGUI:
    """算术表达式翻译器GUI主类"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("语法制导翻译—算术表达式逆波兰式的翻译 (学号: 2022112519)")
        self.root.geometry("900x700")
        
        # 初始化组件
        self.grammar = AttributeGrammar()
        self.translator = SyntaxDirectedTranslator()
        self.evaluator = PostfixEvaluator()
        
        # 创建界面
        self.create_widgets()
        
        # 设置默认显示
        self.show_grammar()
    
    def create_widgets(self):
        """创建GUI组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="语法制导翻译—算术表达式逆波兰式的翻译", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 左侧控制面板
        control_frame = ttk.LabelFrame(main_frame, text="功能控制", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 功能按钮
        ttk.Button(control_frame, text="显示属性文法", 
                  command=self.show_grammar, width=15).grid(row=0, column=0, pady=5, sticky=tk.W+tk.E)
        
        ttk.Button(control_frame, text="表达式翻译", 
                  command=self.show_translation, width=15).grid(row=1, column=0, pady=5, sticky=tk.W+tk.E)
        
        ttk.Button(control_frame, text="逆波兰式求值", 
                  command=self.show_evaluation, width=15).grid(row=2, column=0, pady=5, sticky=tk.W+tk.E)
        
        # 分隔线
        ttk.Separator(control_frame, orient='horizontal').grid(row=3, column=0, sticky=(tk.W, tk.E), pady=10)
        
        # 表达式输入
        ttk.Label(control_frame, text="中缀表达式:").grid(row=4, column=0, sticky=tk.W, pady=(5, 0))
        self.expression_entry = ttk.Entry(control_frame, width=20)
        self.expression_entry.grid(row=5, column=0, pady=5, sticky=tk.W+tk.E)
        self.expression_entry.bind('<Return>', lambda e: self.translate_expression())
        
        ttk.Button(control_frame, text="翻译", 
                  command=self.translate_expression, width=15).grid(row=6, column=0, pady=5, sticky=tk.W+tk.E)
        
        # 逆波兰式输入
        ttk.Label(control_frame, text="逆波兰式:").grid(row=7, column=0, sticky=tk.W, pady=(10, 0))
        self.postfix_entry = ttk.Entry(control_frame, width=20)
        self.postfix_entry.grid(row=8, column=0, pady=5, sticky=tk.W+tk.E)
        self.postfix_entry.bind('<Return>', lambda e: self.evaluate_postfix())
        
        # 变量设置
        ttk.Label(control_frame, text="变量设置 (格式: a=1,b=2):").grid(row=9, column=0, sticky=tk.W, pady=(10, 0))
        self.variables_entry = ttk.Entry(control_frame, width=20)
        self.variables_entry.grid(row=10, column=0, pady=5, sticky=tk.W+tk.E)
        
        ttk.Button(control_frame, text="求值", 
                  command=self.evaluate_postfix, width=15).grid(row=11, column=0, pady=5, sticky=tk.W+tk.E)
        
        # 示例按钮
        ttk.Button(control_frame, text="加载示例", 
                  command=self.load_examples, width=15).grid(row=12, column=0, pady=(10, 5), sticky=tk.W+tk.E)
        
        # 清空按钮
        ttk.Button(control_frame, text="清空", 
                  command=self.clear_all, width=15).grid(row=13, column=0, pady=5, sticky=tk.W+tk.E)
        
        # 右侧显示区域
        display_frame = ttk.LabelFrame(main_frame, text="显示区域", padding="10")
        display_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        display_frame.columnconfigure(0, weight=1)
        display_frame.rowconfigure(0, weight=1)
        
        # 文本显示区域
        self.display_text = scrolledtext.ScrolledText(display_frame, wrap=tk.WORD, 
                                                     width=60, height=35, font=("Consolas", 10))
        self.display_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def show_grammar(self):
        """显示属性文法"""
        self.display_text.delete(1.0, tk.END)
        grammar_text = self.grammar.display_grammar()
        self.display_text.insert(tk.END, grammar_text)
        self.status_var.set("已显示属性文法")
    
    def show_translation(self):
        """显示翻译功能说明"""
        self.display_text.delete(1.0, tk.END)
        help_text = """
算术表达式翻译功能说明
========================

功能：将中缀算术表达式翻译为逆波兰式（后缀表达式）

支持的运算符：
- +  : 加法
- -  : 减法  
- *  : 乘法
- /  : 除法
- @  : 取负（一元运算符）
- () : 括号

支持的操作数：
- 数字常量：如 123, 3.14
- 变量标识符：如 a, x1, var_name

使用方法：
1. 在左侧"中缀表达式"输入框中输入表达式
2. 点击"翻译"按钮或按回车键
3. 翻译结果将显示在此区域

示例：
中缀表达式: a + b * c
逆波兰式: a b c * +

中缀表达式: (a + b) * (c - d)  
逆波兰式: a b + c d - *

中缀表达式: @a + b
逆波兰式: a @ b +

注意事项：
- 表达式中不能有空格以外的非法字符
- 括号必须匹配
- 运算符必须有相应的操作数
"""
        self.display_text.insert(tk.END, help_text)
        self.status_var.set("已显示翻译功能说明")
    
    def show_evaluation(self):
        """显示求值功能说明"""
        self.display_text.delete(1.0, tk.END)
        help_text = """
逆波兰式求值功能说明
====================

功能：计算逆波兰式表达式的值

使用方法：
1. 在"逆波兰式"输入框中输入逆波兰式表达式（用空格分隔）
2. 如果表达式中包含变量，在"变量设置"输入框中设置变量值
   格式：变量名=值，多个变量用逗号分隔，如：a=3,b=4,c=2
3. 点击"求值"按钮
4. 计算结果和详细步骤将显示在此区域

支持的运算符：
- +  : 加法（二元）
- -  : 减法（二元）
- *  : 乘法（二元）
- /  : 除法（二元）
- @  : 取负（一元）

示例：
逆波兰式: 3 4 +
结果: 7

逆波兰式: a b c * +  (设置 a=3, b=4, c=2)
结果: 11
计算过程: 3 + (4 * 2) = 3 + 8 = 11

逆波兰式: 5 @
结果: -5

注意事项：
- 逆波兰式中的元素必须用空格分隔
- 使用的变量必须先设置值
- 除法运算中除数不能为0
"""
        self.display_text.insert(tk.END, help_text)
        self.status_var.set("已显示求值功能说明")
    
    def translate_expression(self):
        """翻译中缀表达式为逆波兰式"""
        expression = self.expression_entry.get().strip()
        if not expression:
            messagebox.showwarning("警告", "请输入中缀表达式")
            return
        
        # 执行翻译
        result = self.translator.translate(expression)
        
        # 显示结果
        self.display_text.delete(1.0, tk.END)
        self.display_text.insert(tk.END, f"中缀表达式翻译结果\n")
        self.display_text.insert(tk.END, f"{'='*30}\n\n")
        self.display_text.insert(tk.END, f"输入的中缀表达式：{expression}\n\n")
        
        if result.startswith("语法错误") or result.startswith("翻译错误"):
            self.display_text.insert(tk.END, f"翻译失败：\n{result}\n")
            self.status_var.set("翻译失败")
        else:
            self.display_text.insert(tk.END, f"翻译成功！\n")
            self.display_text.insert(tk.END, f"生成的逆波兰式：{result}\n\n")
            
            # 自动填入逆波兰式输入框
            self.postfix_entry.delete(0, tk.END)
            self.postfix_entry.insert(0, result)
            
            self.display_text.insert(tk.END, f"提示：逆波兰式已自动填入求值输入框，可直接进行求值。\n")
            self.status_var.set("翻译成功")
    
    def parse_variables(self, var_string):
        """解析变量字符串"""
        variables = {}
        if not var_string.strip():
            return variables
        
        try:
            pairs = var_string.split(',')
            for pair in pairs:
                if '=' in pair:
                    name, value = pair.split('=', 1)
                    name = name.strip()
                    value = float(value.strip())
                    variables[name] = value
        except ValueError as e:
            raise ValueError(f"变量格式错误：{str(e)}")
        
        return variables

    def evaluate_postfix(self):
        """求值逆波兰式"""
        postfix = self.postfix_entry.get().strip()
        if not postfix:
            messagebox.showwarning("警告", "请输入逆波兰式")
            return

        var_string = self.variables_entry.get().strip()

        try:
            # 解析变量
            variables = self.parse_variables(var_string)

            # 求值
            result, steps = self.evaluator.evaluate_with_steps(postfix, variables)

            # 显示结果
            self.display_text.delete(1.0, tk.END)
            self.display_text.insert(tk.END, f"逆波兰式求值结果\n")
            self.display_text.insert(tk.END, f"{'='*30}\n\n")
            self.display_text.insert(tk.END, f"输入的逆波兰式：{postfix}\n")

            if variables:
                self.display_text.insert(tk.END, f"变量设置：{variables}\n")

            self.display_text.insert(tk.END, f"\n计算详细步骤：\n")
            self.display_text.insert(tk.END, f"{'-'*30}\n")

            for step in steps:
                self.display_text.insert(tk.END, f"{step}\n")

            self.display_text.insert(tk.END, f"\n最终结果：{result}\n")

            if isinstance(result, (int, float)) and not str(result).startswith("错误"):
                self.status_var.set(f"求值成功，结果：{result}")
            else:
                self.status_var.set("求值失败")

        except ValueError as e:
            messagebox.showerror("错误", str(e))
            self.status_var.set("变量格式错误")

    def load_examples(self):
        """加载示例"""
        examples = [
            ("a + b * c", "a=2,b=3,c=4"),
            ("(a + b) * (c - d)", "a=1,b=2,c=5,d=3"),
            ("@a + b / c", "a=6,b=8,c=2"),
            ("a * b + c * d", "a=2,b=3,c=4,d=5"),
            ("((a + b) * c - d) / e", "a=1,b=2,c=3,d=4,e=5")
        ]

        # 创建示例选择窗口
        example_window = tk.Toplevel(self.root)
        example_window.title("选择示例")
        example_window.geometry("400x300")
        example_window.transient(self.root)
        example_window.grab_set()

        ttk.Label(example_window, text="请选择一个示例：", font=("Arial", 12)).pack(pady=10)

        # 示例列表
        listbox = tk.Listbox(example_window, height=8, font=("Consolas", 10))
        listbox.pack(pady=10, padx=20, fill=tk.BOTH, expand=True)

        for i, (expr, vars_) in enumerate(examples):
            listbox.insert(tk.END, f"{i+1}. {expr}  (变量: {vars_})")

        def select_example():
            selection = listbox.curselection()
            if selection:
                idx = selection[0]
                expr, vars_ = examples[idx]
                self.expression_entry.delete(0, tk.END)
                self.expression_entry.insert(0, expr)
                self.variables_entry.delete(0, tk.END)
                self.variables_entry.insert(0, vars_)
                example_window.destroy()
                self.status_var.set(f"已加载示例 {idx+1}")

        ttk.Button(example_window, text="选择", command=select_example).pack(pady=10)
        ttk.Button(example_window, text="取消", command=example_window.destroy).pack()

        # 双击选择
        listbox.bind('<Double-Button-1>', lambda e: select_example())

    def clear_all(self):
        """清空所有输入和显示"""
        self.expression_entry.delete(0, tk.END)
        self.postfix_entry.delete(0, tk.END)
        self.variables_entry.delete(0, tk.END)
        self.display_text.delete(1.0, tk.END)
        self.evaluator.clear_variables()
        self.status_var.set("已清空")

def main():
    """主函数"""
    root = tk.Tk()
    app = ArithmeticTranslatorGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 设置窗口关闭事件
    def on_closing():
        if messagebox.askokcancel("退出", "确定要退出程序吗？"):
            root.destroy()

    root.protocol("WM_DELETE_WINDOW", on_closing)

    # 启动GUI
    root.mainloop()

if __name__ == "__main__":
    main()
