# -*- coding: utf-8 -*-
"""
逆波兰式求值器模块
学号：2022112519
课程设计：语法制导翻译—算术表达式逆波兰式的翻译
"""

import re

class PostfixEvaluator:
    """逆波兰式求值器"""
    
    def __init__(self):
        self.variables = {}  # 存储变量值
        
    def set_variable(self, name, value):
        """设置变量值"""
        try:
            self.variables[name] = float(value)
            return True
        except ValueError:
            return False
    
    def get_variables(self):
        """获取所有变量"""
        return self.variables.copy()
    
    def clear_variables(self):
        """清空所有变量"""
        self.variables.clear()
    
    def is_number(self, s):
        """判断字符串是否为数字"""
        try:
            float(s)
            return True
        except ValueError:
            return False
    
    def is_operator(self, s):
        """判断字符串是否为运算符"""
        return s in ['+', '-', '*', '/', '@']
    
    def evaluate(self, postfix_expression, variable_values=None):
        """
        计算逆波兰式的值
        
        Args:
            postfix_expression: 逆波兰式字符串，用空格分隔
            variable_values: 变量值字典，可选
        
        Returns:
            计算结果或错误信息
        """
        try:
            # 更新变量值
            if variable_values:
                self.variables.update(variable_values)
            
            # 分割逆波兰式
            tokens = postfix_expression.strip().split()
            if not tokens:
                return "错误：逆波兰式为空"
            
            # 使用栈进行计算
            stack = []
            
            for i, token in enumerate(tokens):
                if self.is_number(token):
                    # 数字直接入栈
                    stack.append(float(token))
                    
                elif token in self.variables:
                    # 变量入栈
                    stack.append(self.variables[token])
                    
                elif re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', token):
                    # 未定义的变量
                    return f"错误：变量 '{token}' 未定义"
                    
                elif self.is_operator(token):
                    # 运算符处理
                    if token == '@':
                        # 取负运算（一元运算符）
                        if len(stack) < 1:
                            return f"错误：取负运算符 '@' 缺少操作数（位置 {i+1}）"
                        operand = stack.pop()
                        result = -operand
                        stack.append(result)
                        
                    else:
                        # 二元运算符
                        if len(stack) < 2:
                            return f"错误：运算符 '{token}' 缺少操作数（位置 {i+1}）"
                        
                        # 注意：栈中后进先出，所以第二个操作数先出栈
                        operand2 = stack.pop()
                        operand1 = stack.pop()
                        
                        if token == '+':
                            result = operand1 + operand2
                        elif token == '-':
                            result = operand1 - operand2
                        elif token == '*':
                            result = operand1 * operand2
                        elif token == '/':
                            if operand2 == 0:
                                return f"错误：除零错误（位置 {i+1}）"
                            result = operand1 / operand2
                        
                        stack.append(result)
                else:
                    return f"错误：无法识别的符号 '{token}'（位置 {i+1}）"
            
            # 检查最终结果
            if len(stack) == 1:
                return stack[0]
            elif len(stack) == 0:
                return "错误：表达式无结果"
            else:
                return f"错误：表达式不完整，栈中还有 {len(stack)} 个操作数"
                
        except Exception as e:
            return f"计算错误：{str(e)}"
    
    def evaluate_with_steps(self, postfix_expression, variable_values=None):
        """
        计算逆波兰式的值，并返回详细步骤
        
        Returns:
            (result, steps) 元组，其中 steps 是计算步骤列表
        """
        try:
            # 更新变量值
            if variable_values:
                self.variables.update(variable_values)
            
            # 分割逆波兰式
            tokens = postfix_expression.strip().split()
            if not tokens:
                return "错误：逆波兰式为空", []
            
            # 使用栈进行计算，记录步骤
            stack = []
            steps = []
            steps.append(f"开始计算逆波兰式: {postfix_expression}")
            steps.append("初始化空栈: []")
            
            for i, token in enumerate(tokens):
                if self.is_number(token):
                    # 数字直接入栈
                    value = float(token)
                    stack.append(value)
                    steps.append(f"步骤 {i+1}: 读取数字 {token}，入栈")
                    steps.append(f"        当前栈: {stack}")
                    
                elif token in self.variables:
                    # 变量入栈
                    value = self.variables[token]
                    stack.append(value)
                    steps.append(f"步骤 {i+1}: 读取变量 {token} = {value}，入栈")
                    steps.append(f"        当前栈: {stack}")
                    
                elif re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', token):
                    # 未定义的变量
                    return f"错误：变量 '{token}' 未定义", steps
                    
                elif self.is_operator(token):
                    # 运算符处理
                    if token == '@':
                        # 取负运算（一元运算符）
                        if len(stack) < 1:
                            return f"错误：取负运算符 '@' 缺少操作数", steps
                        operand = stack.pop()
                        result = -operand
                        stack.append(result)
                        steps.append(f"步骤 {i+1}: 读取运算符 @，取负运算")
                        steps.append(f"        弹出操作数: {operand}")
                        steps.append(f"        计算: @{operand} = {result}")
                        steps.append(f"        结果入栈，当前栈: {stack}")
                        
                    else:
                        # 二元运算符
                        if len(stack) < 2:
                            return f"错误：运算符 '{token}' 缺少操作数", steps
                        
                        operand2 = stack.pop()
                        operand1 = stack.pop()
                        
                        if token == '+':
                            result = operand1 + operand2
                        elif token == '-':
                            result = operand1 - operand2
                        elif token == '*':
                            result = operand1 * operand2
                        elif token == '/':
                            if operand2 == 0:
                                return f"错误：除零错误", steps
                            result = operand1 / operand2
                        
                        stack.append(result)
                        steps.append(f"步骤 {i+1}: 读取运算符 {token}")
                        steps.append(f"        弹出操作数: {operand2}, {operand1}")
                        steps.append(f"        计算: {operand1} {token} {operand2} = {result}")
                        steps.append(f"        结果入栈，当前栈: {stack}")
                else:
                    return f"错误：无法识别的符号 '{token}'", steps
            
            # 检查最终结果
            if len(stack) == 1:
                steps.append(f"计算完成，最终结果: {stack[0]}")
                return stack[0], steps
            elif len(stack) == 0:
                return "错误：表达式无结果", steps
            else:
                return f"错误：表达式不完整，栈中还有 {len(stack)} 个操作数", steps
                
        except Exception as e:
            return f"计算错误：{str(e)}", steps

if __name__ == "__main__":
    # 测试逆波兰式求值器
    evaluator = PostfixEvaluator()
    
    # 设置变量值
    evaluator.set_variable('a', 3)
    evaluator.set_variable('b', 4)
    evaluator.set_variable('c', 2)
    
    test_expressions = [
        "3 4 +",
        "3 4 2 * +",
        "a b c * +",
        "a b + c *",
        "5 @"
    ]
    
    for expr in test_expressions:
        result, steps = evaluator.evaluate_with_steps(expr)
        print(f"逆波兰式: {expr}")
        print(f"计算结果: {result}")
        print("计算步骤:")
        for step in steps:
            print(f"  {step}")
        print("-" * 50)
