<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语法制导翻译系统流程图</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }
        
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        
        .flowchart {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 30px 0;
            padding: 20px;
            background: #fafafa;
            border-radius: 8px;
        }
        
        .module-chart {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            margin: 10px;
            border-radius: 8px;
            text-align: center;
            min-width: 120px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            font-weight: bold;
        }
        
        .main-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            font-size: 18px;
            min-width: 200px;
        }
        
        .sub-box {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            font-size: 14px;
            min-width: 100px;
        }
        
        .process-box {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: #333;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 6px;
            border: 2px solid #2ecc71;
            font-weight: bold;
        }
        
        .decision-box {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: #333;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 20px;
            border: 2px solid #e74c3c;
            font-weight: bold;
            transform: skew(-10deg);
        }
        
        .start-end-box {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: #333;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 25px;
            border: 2px solid #16a085;
            font-weight: bold;
        }
        
        .arrow {
            font-size: 24px;
            color: #3498db;
            margin: 5px 0;
            font-weight: bold;
        }
        
        .horizontal-arrow {
            font-size: 24px;
            color: #3498db;
            margin: 0 10px;
            font-weight: bold;
        }
        
        .connection-line {
            width: 2px;
            height: 30px;
            background: #3498db;
            margin: 5px auto;
        }
        
        .branch {
            display: flex;
            justify-content: space-around;
            width: 100%;
            margin: 20px 0;
        }
        
        .branch-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            margin: 0 10px;
        }
        
        .info-box {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        
        .student-info {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="student-info">
            <h1>语法制导翻译—算术表达式逆波兰式的翻译</h1>
            <p><strong>学号：2022112519</strong> | <strong>课程：编译原理课程设计</strong></p>
        </div>

        <h2>1. 系统功能模块图</h2>
        <div class="flowchart">
            <div class="box main-box">主程序模块<br>(main.py)</div>
            <div class="arrow">↓</div>
            <div class="connection-line"></div>
            
            <div class="module-chart">
                <div class="box sub-box">属性文法<br>显示模块<br>(grammar.py)</div>
                <div class="box sub-box">语法分析<br>翻译模块<br>(parser.py)</div>
                <div class="box sub-box">逆波兰式<br>求值模块<br>(evaluator.py)</div>
            </div>
            
            <div class="info-box">
                <strong>模块说明：</strong><br>
                • 主程序模块：提供GUI界面，协调各个功能模块<br>
                • 属性文法模块：定义和显示算术表达式的属性文法<br>
                • 语法分析模块：实现词法分析和语法制导翻译<br>
                • 求值模块：计算逆波兰式表达式的值
            </div>
        </div>

        <h2>2. 语法制导翻译流程图</h2>
        <div class="flowchart">
            <div class="start-end-box">开始</div>
            <div class="arrow">↓</div>
            
            <div class="process-box">输入中缀表达式</div>
            <div class="arrow">↓</div>
            
            <div class="process-box">词法分析<br>(正则表达式识别词法单元)</div>
            <div class="arrow">↓</div>
            
            <div class="decision-box">词法分析成功？</div>
            
            <div class="branch">
                <div class="branch-item">
                    <div style="color: #e74c3c; font-weight: bold;">否</div>
                    <div class="arrow">↓</div>
                    <div class="process-box" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);">
                        输出词法错误信息
                    </div>
                </div>
                <div class="branch-item">
                    <div style="color: #27ae60; font-weight: bold;">是</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">语法分析<br>(递归下降分析)</div>
                </div>
            </div>
            
            <div class="arrow">↓</div>
            <div class="decision-box">语法分析成功？</div>
            
            <div class="branch">
                <div class="branch-item">
                    <div style="color: #e74c3c; font-weight: bold;">否</div>
                    <div class="arrow">↓</div>
                    <div class="process-box" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);">
                        输出语法错误信息<br>和错误位置
                    </div>
                </div>
                <div class="branch-item">
                    <div style="color: #27ae60; font-weight: bold;">是</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">语义动作<br>(生成逆波兰式)</div>
                </div>
            </div>
            
            <div class="arrow">↓</div>
            <div class="process-box">输出逆波兰式</div>
            <div class="arrow">↓</div>
            <div class="start-end-box">结束</div>
        </div>

        <h2>3. 逆波兰式求值流程图</h2>
        <div class="flowchart">
            <div class="start-end-box">开始</div>
            <div class="arrow">↓</div>
            
            <div class="process-box">输入逆波兰式和变量值</div>
            <div class="arrow">↓</div>
            
            <div class="process-box">初始化栈</div>
            <div class="arrow">↓</div>
            
            <div class="process-box">从左到右扫描逆波兰式</div>
            <div class="arrow">↓</div>
            
            <div class="decision-box">是操作数？</div>
            
            <div class="branch">
                <div class="branch-item">
                    <div style="color: #27ae60; font-weight: bold;">是</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">操作数入栈</div>
                </div>
                <div class="branch-item">
                    <div style="color: #e74c3c; font-weight: bold;">否</div>
                    <div class="arrow">↓</div>
                    <div class="decision-box">是运算符？</div>
                </div>
            </div>
            
            <div class="branch">
                <div class="branch-item">
                    <div style="color: #27ae60; font-weight: bold;">是</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">弹出操作数<br>执行运算<br>结果入栈</div>
                </div>
                <div class="branch-item">
                    <div style="color: #e74c3c; font-weight: bold;">否</div>
                    <div class="arrow">↓</div>
                    <div class="process-box" style="background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);">
                        输出错误信息
                    </div>
                </div>
            </div>
            
            <div class="arrow">↓</div>
            <div class="decision-box">扫描完毕？</div>
            
            <div class="branch">
                <div class="branch-item">
                    <div style="color: #e74c3c; font-weight: bold;">否</div>
                    <div class="arrow">↓</div>
                    <div style="color: #3498db; font-weight: bold;">继续扫描 →</div>
                </div>
                <div class="branch-item">
                    <div style="color: #27ae60; font-weight: bold;">是</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">输出栈顶元素<br>(最终结果)</div>
                </div>
            </div>
            
            <div class="arrow">↓</div>
            <div class="start-end-box">结束</div>
        </div>

        <h2>4. 递归下降分析详细流程</h2>
        <div class="flowchart">
            <div class="info-box">
                <strong>文法规则：</strong><br>
                E → E + T | E - T | T<br>
                T → T * F | T / F | F<br>
                F → @ F | ( E ) | i | d
            </div>
            
            <div class="start-end-box">parse_expression()</div>
            <div class="arrow">↓</div>
            
            <div class="process-box">调用 parse_term()</div>
            <div class="arrow">↓</div>
            
            <div class="decision-box">当前符号是 + 或 - ？</div>
            
            <div class="branch">
                <div class="branch-item">
                    <div style="color: #27ae60; font-weight: bold;">是</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">保存运算符<br>前进到下一符号<br>调用 parse_term()<br>输出运算符到逆波兰式</div>
                </div>
                <div class="branch-item">
                    <div style="color: #e74c3c; font-weight: bold;">否</div>
                    <div class="arrow">↓</div>
                    <div class="process-box">返回</div>
                </div>
            </div>
        </div>

        <div class="info-box">
            <strong>说明：</strong><br>
            • parse_term() 和 parse_factor() 采用类似的递归下降方法<br>
            • 每个非终结符对应一个解析函数<br>
            • 在语法分析的同时执行语义动作生成逆波兰式<br>
            • 错误处理贯穿整个分析过程
        </div>
    </div>
</body>
</html>
