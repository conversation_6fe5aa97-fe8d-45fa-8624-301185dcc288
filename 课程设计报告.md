# 编译原理课程设计报告

**题目：语法制导翻译—算术表达式逆波兰式的翻译**

**学号：2022112519**  
**姓名：[请填写你的姓名]**  
**班级：[请填写你的班级]**  
**专业：[请填写你的专业]**  
**日期：2024年 [月] 日**

---

## （一）题目

语法制导翻译—算术表达式的逆波兰式的翻译

通过课程设计，理解掌握语法制导翻译的原理与应用，理解中间代码（逆波兰式）与中间代码的生成，掌握把表达式翻译出中间代码的算法与思想，并设计出错处理方法。

## （二）课程设计内容

1. 从左至右扫描中缀算术表达式，把中缀表达式翻译生成"后缀表达式（逆波兰式）"；
2. 经语法分析，如果中缀表达式出现错误，给出错误信息与错误位置；
3. 语法制导翻译：算术表达式主要包括：加、减、乘、除、取负运算5种运算符，其中包括小括号、常量与变量。

程序的功能主要包括三个：
- **A、显示属性文法**：给出算术表达式的属性文法，程序显示属性文法。
- **B、算术表达的输入与编辑、翻译**：编辑中缀式算术表达式、利用语法制导翻译技术，把中缀式翻译为逆波兰式。
- **C、逆波兰式的求值**：执行逆波兰式并给出求值结果。

4. 系统要设计GUI，操作友好。
5. 程序测试与运行：程序要有正确的运行结果。

## （三）课程设计要求

1. 认真阅读理解本课程设计的要求，理解课程设计内容与相关理论，并完成该课程设计报告；
2. 根据该模板，完成课程设计报告的撰写，要求逻辑合理，排版规范，叙述清楚。

## （四）课程设计算法思路

### 4.1 总体设计思路

本课程设计采用模块化设计思想，将整个系统分为以下几个核心模块：

1. **属性文法模块（grammar.py）**：定义和显示算术表达式的属性文法
2. **语法分析模块（parser.py）**：实现词法分析和语法制导翻译
3. **求值模块（evaluator.py）**：实现逆波兰式的求值计算
4. **GUI界面模块（main.py）**：提供友好的图形用户界面

### 4.2 词法分析算法

采用正则表达式进行词法分析，识别以下词法单元：
- 数字常量：`\d+(\.\d*)?`
- 变量标识符：`[a-zA-Z_][a-zA-Z0-9_]*`
- 运算符：`+`, `-`, `*`, `/`, `@`
- 括号：`(`, `)`

### 4.3 语法分析算法

采用递归下降分析法，文法规则如下：
```
E → E + T | E - T | T
T → T * F | T / F | F  
F → @ F | ( E ) | i | d
```

### 4.4 语法制导翻译算法

在语法分析的同时，通过语义动作生成逆波兰式：
- 对于二元运算符：先处理操作数，再输出运算符
- 对于一元运算符：先处理操作数，再输出运算符
- 对于括号：只处理括号内的表达式

### 4.5 逆波兰式求值算法

使用栈结构进行求值：
1. 从左到右扫描逆波兰式
2. 遇到操作数则入栈
3. 遇到运算符则弹出相应数量的操作数进行计算，结果入栈
4. 最终栈中剩余的唯一元素即为结果

## （五）系统功能与算法流程图

### 5.1 系统功能模块图

```
┌─────────────────────────────────────┐
│           主程序模块                │
│         (main.py)                   │
└─────────────┬───────────────────────┘
              │
    ┌─────────┼─────────┐
    │         │         │
┌───▼───┐ ┌──▼───┐ ┌───▼────┐
│属性文法│ │语法  │ │逆波兰式│
│显示模块│ │分析  │ │求值模块│
│       │ │翻译  │ │        │
│grammar│ │模块  │ │evaluator│
└───────┘ │parser│ └────────┘
          └──────┘
```

### 5.2 语法制导翻译流程图

```
开始
  ↓
输入中缀表达式
  ↓
词法分析
  ↓
语法分析（递归下降）
  ↓
语义动作（生成逆波兰式）
  ↓
输出逆波兰式
  ↓
结束
```

### 5.3 逆波兰式求值流程图

```
开始
  ↓
输入逆波兰式
  ↓
初始化栈
  ↓
从左到右扫描
  ↓
是操作数？ ──是──→ 入栈
  ↓否
是运算符？ ──是──→ 弹出操作数，计算，结果入栈
  ↓否
扫描完毕？ ──否──→ 继续扫描
  ↓是
输出栈顶元素
  ↓
结束
```

## （六）程序源码

### 6.1 属性文法模块（grammar.py）

```python
# 属性文法定义模块
class AttributeGrammar:
    def __init__(self):
        self.grammar_rules = [
            "E → E + T    { E.code = E₁.code || T.code || '+' }",
            "E → E - T    { E.code = E₁.code || T.code || '-' }",
            "E → T        { E.code = T.code }",
            "T → T * F    { T.code = T₁.code || F.code || '*' }",
            "T → T / F    { T.code = T₁.code || F.code || '/' }",
            "T → F        { T.code = F.code }",
            "F → @ F      { F.code = F₁.code || '@' }",
            "F → ( E )    { F.code = E.code }",
            "F → i        { F.code = i.lexval }",
            "F → d        { F.code = d.lexval }"
        ]
    # ... 其他方法
```

### 6.2 语法分析模块（parser.py）

```python
# 语法制导翻译器
class SyntaxDirectedTranslator:
    def translate(self, expression):
        # 词法分析
        lexer = LexicalAnalyzer()
        self.tokens = lexer.tokenize(expression)
        
        # 语法分析和翻译
        self.parse_expression()
        
        return ' '.join(self.postfix_code)
    
    def parse_expression(self):
        # E → E + T | E - T | T
        self.parse_term()
        while self.current_token and self.current_token.type in ['PLUS', 'MINUS']:
            operator = self.current_token.type
            self.advance()
            self.parse_term()
            self.postfix_code.append('+' if operator == 'PLUS' else '-')
    # ... 其他方法
```

### 6.3 求值模块（evaluator.py）

```python
# 逆波兰式求值器
class PostfixEvaluator:
    def evaluate(self, postfix_expression, variable_values=None):
        tokens = postfix_expression.strip().split()
        stack = []
        
        for token in tokens:
            if self.is_number(token):
                stack.append(float(token))
            elif token in self.variables:
                stack.append(self.variables[token])
            elif self.is_operator(token):
                # 处理运算符
                if token == '@':
                    operand = stack.pop()
                    stack.append(-operand)
                else:
                    operand2 = stack.pop()
                    operand1 = stack.pop()
                    # 执行相应运算
                    stack.append(result)
        
        return stack[0]
    # ... 其他方法
```

**注：完整源码请参考项目文件，这里仅展示核心算法部分。**

## （七）程序运行结果

### 7.1 属性文法显示功能

![属性文法显示](screenshots/grammar_display.png)

程序成功显示了完整的算术表达式属性文法，包括：
- 10条文法产生式及其语义规则
- 终结符集合：{+, -, *, /, @, (, ), i, d}
- 非终结符集合：{E, T, F}
- 详细的文法说明

### 7.2 表达式翻译功能

![表达式翻译](screenshots/translation.png)

测试用例及结果：
1. `a + b * c` → `a b c * +`
2. `(a + b) * (c - d)` → `a b + c d - *`
3. `@a + b / c` → `a @ b c / +`
4. `((a + b) * c - d) / e` → `a b + c * d - e /`

### 7.3 逆波兰式求值功能

![逆波兰式求值](screenshots/evaluation.png)

测试用例及结果：
1. `3 4 +` = 7
2. `a b c * +` (a=2, b=3, c=4) = 14
3. `a b + c d - *` (a=1, b=2, c=5, d=3) = 6

### 7.4 错误处理功能

![错误处理](screenshots/error_handling.png)

程序能够正确识别并报告各种语法错误：
- 括号不匹配：`(a + b` → "期望 )，但得到 文件结束"
- 运算符缺少操作数：`a +` → "意外的表达式结束"
- 非法字符：`a + & b` → "无法识别的字符"

### 7.5 完整工作流程测试

程序成功完成了从中缀表达式到逆波兰式翻译，再到求值的完整流程，所有测试用例均通过验证。

## （八）课程设计体会

通过本次课程设计，我深入理解了编译原理中语法制导翻译的核心概念和实现方法：

1. **理论与实践结合**：将课堂学习的文法理论、语法分析算法等知识应用到实际编程中，加深了对编译原理的理解。

2. **算法设计能力提升**：通过实现递归下降分析器和逆波兰式求值算法，提高了算法设计和实现能力。

3. **软件工程思维**：采用模块化设计，提高了代码的可读性和可维护性，培养了良好的编程习惯。

4. **问题解决能力**：在实现过程中遇到的各种技术难题，通过查阅资料和反复调试得到解决，提高了独立解决问题的能力。

5. **用户体验意识**：设计友好的GUI界面和完善的错误处理机制，培养了用户体验意识。

本次课程设计不仅巩固了编译原理的理论知识，更重要的是提高了将理论转化为实践的能力，为今后的学习和工作奠定了良好的基础。

---

**注：请根据实际情况填写个人信息，并添加程序运行截图。**
