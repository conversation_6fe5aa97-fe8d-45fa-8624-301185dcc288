# -*- coding: utf-8 -*-
"""
属性文法定义模块
学号：2022112519
课程设计：语法制导翻译—算术表达式逆波兰式的翻译
"""

class AttributeGrammar:
    """属性文法类，定义算术表达式的属性文法"""
    
    def __init__(self):
        self.grammar_rules = [
            "E → E + T    { E.code = E₁.code || T.code || '+' }",
            "E → E - T    { E.code = E₁.code || T.code || '-' }",
            "E → T        { E.code = T.code }",
            "T → T * F    { T.code = T₁.code || F.code || '*' }",
            "T → T / F    { T.code = T₁.code || F.code || '/' }",
            "T → F        { T.code = F.code }",
            "F → @ F      { F.code = F₁.code || '@' }",
            "F → ( E )    { F.code = E.code }",
            "F → i        { F.code = i.lexval }",
            "F → d        { F.code = d.lexval }"
        ]
        
        self.terminals = ['+', '-', '*', '/', '@', '(', ')', 'i', 'd']
        self.non_terminals = ['E', 'T', 'F']
        
        self.description = """
算术表达式属性文法说明：
- E: 表达式 (Expression)
- T: 项 (Term)  
- F: 因子 (Factor)
- +, -, *, /: 四则运算符
- @: 取负运算符
- (, ): 括号
- i: 变量标识符
- d: 数字常量
- ||: 字符串连接操作
- code: 综合属性，表示生成的逆波兰式代码
"""
    
    def get_grammar_rules(self):
        """获取文法规则"""
        return self.grammar_rules
    
    def get_terminals(self):
        """获取终结符"""
        return self.terminals
    
    def get_non_terminals(self):
        """获取非终结符"""
        return self.non_terminals
    
    def get_description(self):
        """获取文法描述"""
        return self.description
    
    def display_grammar(self):
        """格式化显示属性文法"""
        result = "算术表达式属性文法：\n"
        result += "=" * 50 + "\n\n"
        
        result += "文法产生式及语义规则：\n"
        result += "-" * 30 + "\n"
        for i, rule in enumerate(self.grammar_rules, 1):
            result += f"{i:2d}. {rule}\n"
        
        result += "\n终结符集合：\n"
        result += "-" * 15 + "\n"
        result += "{ " + ", ".join(self.terminals) + " }\n"
        
        result += "\n非终结符集合：\n"
        result += "-" * 17 + "\n"
        result += "{ " + ", ".join(self.non_terminals) + " }\n"
        
        result += "\n" + self.description
        
        return result

if __name__ == "__main__":
    # 测试属性文法显示
    grammar = AttributeGrammar()
    print(grammar.display_grammar())
