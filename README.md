# 语法制导翻译—算术表达式逆波兰式的翻译

**学号：2022112519**  
**课程：编译原理课程设计**

## 项目简介

本项目实现了一个完整的算术表达式语法制导翻译系统，能够将中缀算术表达式翻译为逆波兰式（后缀表达式），并对逆波兰式进行求值计算。

## 功能特性

### 1. 属性文法显示
- 显示完整的算术表达式属性文法
- 包含文法产生式、语义规则、终结符和非终结符集合

### 2. 语法制导翻译
- 支持加、减、乘、除、取负运算
- 支持括号改变运算优先级
- 支持数字常量和变量标识符
- 提供详细的错误信息和错误位置

### 3. 逆波兰式求值
- 计算逆波兰式表达式的值
- 支持变量赋值
- 显示详细的计算步骤
- 错误检测和处理

### 4. 图形用户界面
- 友好的GUI界面
- 实时翻译和求值
- 示例加载功能
- 清晰的结果显示

## 文件结构

```
├── main.py          # 主程序文件（GUI界面）
├── grammar.py       # 属性文法定义模块
├── parser.py        # 语法分析器模块
├── evaluator.py     # 逆波兰式求值器模块
├── test_cases.py    # 测试用例模块
└── README.md        # 项目说明文档
```

## 运行环境

- Python 3.6+
- tkinter（通常随Python安装）

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 运行测试
```bash
python test_cases.py
```

### 3. 使用GUI界面

#### 显示属性文法
1. 点击"显示属性文法"按钮
2. 在右侧显示区域查看完整的属性文法

#### 表达式翻译
1. 在"中缀表达式"输入框中输入表达式
2. 点击"翻译"按钮或按回车键
3. 查看翻译结果

#### 逆波兰式求值
1. 在"逆波兰式"输入框中输入逆波兰式
2. 如需使用变量，在"变量设置"框中设置（格式：a=1,b=2）
3. 点击"求值"按钮
4. 查看计算结果和详细步骤

#### 加载示例
1. 点击"加载示例"按钮
2. 从弹出窗口中选择示例
3. 自动填入表达式和变量设置

## 支持的语法

### 运算符
- `+` : 加法
- `-` : 减法
- `*` : 乘法
- `/` : 除法
- `@` : 取负（一元运算符）
- `()` : 括号

### 操作数
- 数字常量：如 `123`, `3.14`
- 变量标识符：如 `a`, `x1`, `var_name`

### 运算符优先级
1. `@` (取负) - 最高优先级
2. `*`, `/` (乘除) - 高优先级
3. `+`, `-` (加减) - 低优先级
4. `()` (括号) - 改变优先级

## 示例

### 翻译示例
```
中缀表达式: a + b * c
逆波兰式: a b c * +

中缀表达式: (a + b) * (c - d)
逆波兰式: a b + c d - *

中缀表达式: @a + b / c
逆波兰式: a @ b c / +
```

### 求值示例
```
逆波兰式: 3 4 +
结果: 7

逆波兰式: a b c * + (a=2, b=3, c=4)
结果: 14
计算过程: 2 + (3 * 4) = 2 + 12 = 14
```

## 错误处理

### 语法错误
- 括号不匹配
- 运算符缺少操作数
- 非法字符
- 表达式不完整

### 求值错误
- 变量未定义
- 除零错误
- 操作数不足
- 操作数过多

## 技术实现

### 词法分析
- 使用正则表达式进行词法分析
- 识别数字、标识符、运算符等词法单元

### 语法分析
- 采用递归下降分析法
- 实现语法制导翻译
- 生成逆波兰式代码

### 求值算法
- 使用栈结构计算逆波兰式
- 支持详细步骤跟踪
- 完善的错误检测

## 课程设计要求对应

1. ✅ **属性文法显示** - 完整显示算术表达式的属性文法
2. ✅ **表达式翻译** - 中缀表达式到逆波兰式的翻译
3. ✅ **逆波兰式求值** - 计算逆波兰式的值
4. ✅ **错误处理** - 语法错误检测和错误位置提示
5. ✅ **GUI界面** - 友好的图形用户界面
6. ✅ **测试验证** - 完整的测试用例和运行结果

## 开发说明

本项目采用模块化设计，各模块职责清晰：
- `grammar.py` - 负责属性文法的定义和显示
- `parser.py` - 负责词法分析和语法制导翻译
- `evaluator.py` - 负责逆波兰式的求值计算
- `main.py` - 负责GUI界面和用户交互
- `test_cases.py` - 负责功能测试和验证

代码结构清晰，注释详细，易于理解和维护。
