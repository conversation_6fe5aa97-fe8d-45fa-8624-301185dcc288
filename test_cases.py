# -*- coding: utf-8 -*-
"""
测试用例模块
学号：2022112519
课程设计：语法制导翻译—算术表达式逆波兰式的翻译
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from grammar import AttributeGrammar
from parser import SyntaxDirectedTranslator
from evaluator import PostfixEvaluator

def test_grammar():
    """测试属性文法显示"""
    print("=" * 60)
    print("测试属性文法显示")
    print("=" * 60)
    
    grammar = AttributeGrammar()
    print(grammar.display_grammar())
    print()

def test_translation():
    """测试语法制导翻译"""
    print("=" * 60)
    print("测试语法制导翻译")
    print("=" * 60)
    
    translator = SyntaxDirectedTranslator()
    
    # 正确的测试用例
    correct_cases = [
        "a + b",
        "a + b * c",
        "a * b + c",
        "(a + b) * c",
        "a * (b + c)",
        "@a",
        "@a + b",
        "a + @b",
        "a - b",
        "a / b",
        "a + b - c",
        "a * b / c",
        "(a + b) * (c - d)",
        "((a + b) * c - d) / e",
        "a + b * c - d / e",
        "123 + 456",
        "3.14 * 2",
        "x1 + y2 * z3"
    ]
    
    print("正确表达式测试：")
    print("-" * 40)
    for i, expr in enumerate(correct_cases, 1):
        result = translator.translate(expr)
        print(f"{i:2d}. 中缀: {expr:<25} => 逆波兰: {result}")
    
    print("\n错误表达式测试：")
    print("-" * 40)
    
    # 错误的测试用例
    error_cases = [
        "a +",           # 缺少操作数
        "+ a",           # 运算符开头
        "a + + b",       # 连续运算符
        "(a + b",        # 括号不匹配
        "a + b)",        # 括号不匹配
        "((a + b)",      # 括号不匹配
        "a + b * ",      # 表达式不完整
        "a @ b",         # 取负运算符使用错误
        "a + & b",       # 非法字符
        "",              # 空表达式
        "   ",           # 空白表达式
    ]
    
    for i, expr in enumerate(error_cases, 1):
        result = translator.translate(expr)
        print(f"{i:2d}. 错误: {expr:<15} => {result}")
    
    print()

def test_evaluation():
    """测试逆波兰式求值"""
    print("=" * 60)
    print("测试逆波兰式求值")
    print("=" * 60)
    
    evaluator = PostfixEvaluator()
    
    # 设置变量值
    variables = {'a': 3, 'b': 4, 'c': 2, 'd': 5, 'e': 1}
    
    # 正确的测试用例
    correct_cases = [
        ("3 4 +", {}),
        ("3 4 *", {}),
        ("3 4 2 * +", {}),
        ("3 4 + 2 *", {}),
        ("a b +", variables),
        ("a b c * +", variables),
        ("a b + c d - *", variables),
        ("5 @", {}),
        ("a @", variables),
        ("10 3 /", {}),
        ("15 3 / 2 +", {}),
        ("2 3 4 * +", {}),
    ]
    
    print("正确逆波兰式测试：")
    print("-" * 50)
    for i, (expr, vars_) in enumerate(correct_cases, 1):
        result, steps = evaluator.evaluate_with_steps(expr, vars_)
        print(f"{i:2d}. 逆波兰式: {expr}")
        if vars_:
            print(f"    变量: {vars_}")
        print(f"    结果: {result}")
        print(f"    步骤数: {len(steps)}")
        print()
    
    print("错误逆波兰式测试：")
    print("-" * 50)
    
    # 错误的测试用例
    error_cases = [
        ("3 +", {}),           # 操作数不足
        ("3 4 5 +", {}),       # 操作数过多
        ("a b +", {}),         # 变量未定义
        ("3 0 /", {}),         # 除零错误
        ("", {}),              # 空表达式
        ("3 & +", {}),         # 非法符号
    ]
    
    for i, (expr, vars_) in enumerate(error_cases, 1):
        result, steps = evaluator.evaluate_with_steps(expr, vars_)
        print(f"{i:2d}. 错误: {expr}")
        print(f"    结果: {result}")
        print()

def test_complete_workflow():
    """测试完整工作流程"""
    print("=" * 60)
    print("测试完整工作流程（翻译 + 求值）")
    print("=" * 60)
    
    translator = SyntaxDirectedTranslator()
    evaluator = PostfixEvaluator()
    
    # 完整测试用例
    test_cases = [
        ("a + b * c", {'a': 2, 'b': 3, 'c': 4}),
        ("(a + b) * (c - d)", {'a': 1, 'b': 2, 'c': 5, 'd': 3}),
        ("@a + b / c", {'a': 6, 'b': 8, 'c': 2}),
        ("a * b + c * d", {'a': 2, 'b': 3, 'c': 4, 'd': 5}),
        ("((a + b) * c - d) / e", {'a': 1, 'b': 2, 'c': 3, 'd': 4, 'e': 5}),
    ]
    
    for i, (infix, variables) in enumerate(test_cases, 1):
        print(f"测试用例 {i}:")
        print(f"中缀表达式: {infix}")
        print(f"变量设置: {variables}")
        
        # 翻译
        postfix = translator.translate(infix)
        print(f"逆波兰式: {postfix}")
        
        if not postfix.startswith("语法错误") and not postfix.startswith("翻译错误"):
            # 求值
            result, steps = evaluator.evaluate_with_steps(postfix, variables)
            print(f"计算结果: {result}")
            
            # 验证计算（手工计算对比）
            if i == 1:  # a + b * c = 2 + 3 * 4 = 2 + 12 = 14
                expected = 2 + 3 * 4
                print(f"期望结果: {expected}")
                print(f"验证: {'通过' if abs(result - expected) < 1e-10 else '失败'}")
            elif i == 2:  # (a + b) * (c - d) = (1 + 2) * (5 - 3) = 3 * 2 = 6
                expected = (1 + 2) * (5 - 3)
                print(f"期望结果: {expected}")
                print(f"验证: {'通过' if abs(result - expected) < 1e-10 else '失败'}")
            elif i == 3:  # @a + b / c = -6 + 8 / 2 = -6 + 4 = -2
                expected = -6 + 8 / 2
                print(f"期望结果: {expected}")
                print(f"验证: {'通过' if abs(result - expected) < 1e-10 else '失败'}")
        
        print("-" * 50)

def main():
    """主测试函数"""
    print("语法制导翻译—算术表达式逆波兰式的翻译")
    print("课程设计测试程序")
    print("学号：2022112519")
    print()
    
    # 运行所有测试
    test_grammar()
    test_translation()
    test_evaluation()
    test_complete_workflow()
    
    print("=" * 60)
    print("所有测试完成！")
    print("=" * 60)

if __name__ == "__main__":
    main()
